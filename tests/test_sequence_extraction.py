#!/usr/bin/env python
"""
test_sequence_extraction.py: Test cases for sequence extraction from RFAntibody output.
"""

import os
import pytest
import pandas as pd
from src.io_rf import extract_sequences_from_scores

def test_extract_sequences_with_heavy_light_antigen():
    """Test extracting sequences when the format is HEAVY:LIGHT:ANTIGEN."""
    # Create a mock dataframe with a sequence in the expected format
    data = {'Job Name': ['test_design'], 'Sequence - rfantibody': ['HEAVY_SEQ:LIGHT_SEQ:ANTIGEN_SEQ']}
    df = pd.DataFrame(data)
    
    # Extract sequences
    sequences = extract_sequences_from_scores(df)
    
    # Check that all sequences were extracted correctly
    assert 'test_design' in sequences
    assert sequences['test_design']['heavy'] == 'HEAVY_SEQ'
    assert sequences['test_design']['light'] == 'LIGHT_SEQ'
    assert sequences['test_design']['antigen'] == 'ANTIGEN_SEQ'


def test_extract_sequences_with_heavy_antigen():
    """Test extracting sequences when the format is HEAVY:ANTIGEN (no light chain)."""
    # Create a mock dataframe with a sequence that has a very long second part (antigen)
    # The threshold in extract_sequences_from_scores is 300, so make the antigen longer
    antigen = 'A' * 400  # Create a 400-character string
    data = {'Job Name': ['test_design'], 'Sequence - rfantibody': [f'HEAVY_SEQ:{antigen}']}
    df = pd.DataFrame(data)
    
    # Extract sequences
    sequences = extract_sequences_from_scores(df)
    
    # Check that sequences were extracted correctly
    assert 'test_design' in sequences
    assert sequences['test_design']['heavy'] == 'HEAVY_SEQ'
    assert sequences['test_design']['light'] == ''
    assert sequences['test_design']['antigen'] == antigen


def test_extract_sequences_with_heavy_light():
    """Test extracting sequences when the format is HEAVY:LIGHT (no antigen in sequence)."""
    # Create a mock dataframe with a sequence that has a short second part (light chain)
    light = 'L' * 110  # Light chain is typically around 110-130 amino acids
    data = {'Job Name': ['test_design'], 'Sequence - rfantibody': [f'HEAVY_SEQ:{light}']}
    df = pd.DataFrame(data)

    # Extract sequences
    sequences = extract_sequences_from_scores(df)

    # Check that sequences were extracted correctly
    assert 'test_design' in sequences
    assert sequences['test_design']['heavy'] == 'HEAVY_SEQ'
    assert sequences['test_design']['light'] == light
    assert sequences['test_design']['antigen'] == ''


def test_extract_sequences_with_separate_columns():
    """Test extracting sequences from separate rfantibody columns."""
    # Create a mock dataframe with separate sequence columns
    data = {
        'Job Name': ['test_design'],
        'Sequence_H - rfantibody': ['HEAVY_SEQ'],
        'Sequence_L - rfantibody': ['LIGHT_SEQ'],
        'Sequence_T - rfantibody': ['ANTIGEN_SEQ']
    }
    df = pd.DataFrame(data)

    # Extract sequences
    sequences = extract_sequences_from_scores(df)

    # Check that all sequences were extracted correctly
    assert 'test_design' in sequences
    assert sequences['test_design']['heavy'] == 'HEAVY_SEQ'
    assert sequences['test_design']['light'] == 'LIGHT_SEQ'
    assert sequences['test_design']['antigen'] == 'ANTIGEN_SEQ'


def test_extract_sequences_ignores_wrong_column_names():
    """Test that sequence extraction only matches exact rfantibody column names."""
    # Create a mock dataframe with columns that contain 'heavy', 'light', 'antigen'
    # but are not the exact rfantibody column names
    data = {
        'Job Name': ['test_design'],
        'heavy_chain_info': ['WRONG_HEAVY'],
        'light_chain_data': ['WRONG_LIGHT'],
        'antigen_target_seq': ['WRONG_ANTIGEN'],
        'Sequence - rfantibody': ['CORRECT_HEAVY:CORRECT_LIGHT:CORRECT_ANTIGEN']
    }
    df = pd.DataFrame(data)

    # Extract sequences
    sequences = extract_sequences_from_scores(df)

    # Check that only the combined sequence column was used, not the wrong columns
    assert 'test_design' in sequences
    assert sequences['test_design']['heavy'] == 'CORRECT_HEAVY'
    assert sequences['test_design']['light'] == 'CORRECT_LIGHT'
    assert sequences['test_design']['antigen'] == 'CORRECT_ANTIGEN'
