#!/usr/bin/env python
"""
test_clean_pdb.py: Unit tests for clean_pdb.py
"""

import os
import json
import pytest
import sys
import tempfile
from Bio.PDB import PDBParser

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.clean_pdb import (
    get_chain_info,
    rename_chains,
    clean_pdb,
    process_rfantibody_pdbs
)

def test_get_chain_info_with_settings(mock_rfantibody_dir):
    """Test getting chain info with settings."""
    # Get the path to the mock PDB file
    pdb_path = os.path.join(mock_rfantibody_dir, "result-batch1", "design1", 
                            "rf2", "samples_design_0_dldesign_0_best.pdb")
    
    # Create settings with chain mappings
    settings = {
        "antigenChain": "X",
        "heavyChain": "H",
        "lightChain": "L"
    }
    
    # Get chain info
    chain_map = get_chain_info(pdb_path, settings)
    
    # Check the mapping
    assert 'H' in chain_map
    assert 'L' in chain_map
    assert 'X' in chain_map
    assert chain_map['H'] == 'H'
    assert chain_map['L'] == 'L'
    assert chain_map['X'] == 'C'  # Antigen chain maps to 'C' consistently


def test_get_chain_info_without_settings(mock_rfantibody_dir):
    """Test getting chain info without settings using chain name-based mapping."""
    # Get the path to the mock PDB file
    pdb_path = os.path.join(mock_rfantibody_dir, "result-batch1", "design1",
                            "rf2", "samples_design_0_dldesign_0_best.pdb")

    # Get chain info without settings
    chain_map = get_chain_info(pdb_path)

    # Check the mapping based on chain names (H=Heavy, L=Light, others=antigen)
    # In our mock, we have chains H, L, X
    assert 'H' in chain_map
    assert 'L' in chain_map
    assert 'X' in chain_map

    # With the new logic: H->H, L->L, X->C (first antigen chain)
    assert chain_map['H'] == 'H'  # Heavy chain stays H
    assert chain_map['L'] == 'L'  # Light chain stays L
    assert chain_map['X'] == 'C'  # Other chains become antigen (C)


def test_rename_chains(mock_rfantibody_dir, temp_dir):
    """Test renaming chains in a PDB file."""
    # Get the path to the mock PDB file
    input_pdb = os.path.join(mock_rfantibody_dir, "result-batch1", "design1", 
                             "rf2", "samples_design_0_dldesign_0_best.pdb")
    output_pdb = os.path.join(temp_dir, "renamed.pdb")
    
    # Define chain mapping
    chain_map = {
        'H': 'A',  # Remap heavy chain to A
        'L': 'B',  # Remap light chain to B
        'X': 'C'   # Remap hetatm chain to C
    }
    
    # Rename chains
    success = rename_chains(input_pdb, output_pdb, chain_map)
    
    # Check that operation was successful
    assert success
    assert os.path.exists(output_pdb)
    
    # Parse the renamed PDB file to verify chains
    parser = PDBParser(QUIET=True)
    structure = parser.get_structure("renamed", output_pdb)
    
    # Get all chain IDs in the renamed PDB
    chain_ids = [chain.id for chain in structure.get_chains()]
    
    # Check that chains were renamed correctly
    if 'H' in chain_map and chain_map['H'] == 'A':
        assert 'A' in chain_ids
    if 'L' in chain_map and chain_map['L'] == 'B':
        assert 'B' in chain_ids


def test_clean_pdb(mock_rfantibody_dir, temp_dir):
    """Test cleaning a PDB file."""
    # Get the path to the mock PDB file
    input_pdb = os.path.join(mock_rfantibody_dir, "result-batch1", "design1", 
                             "rf2", "samples_design_0_dldesign_0_best.pdb")
    output_pdb = os.path.join(temp_dir, "cleaned.pdb")
    
    # Create settings with chain mappings
    settings = {
        "antigenChain": "X",
        "heavyChain": "H",
        "lightChain": "L"
    }
    
    # Clean PDB
    success = clean_pdb(input_pdb, output_pdb, settings)
    
    # Check that operation was successful
    assert success
    assert os.path.exists(output_pdb)
    
    # Parse the cleaned PDB file
    parser = PDBParser(QUIET=True)
    structure = parser.get_structure("cleaned", output_pdb)
    
    # Check for expected properties:
    # 1. Should have standardized chains (A, H, L)
    chain_ids = [chain.id for chain in structure.get_chains()]
    if 'H' in chain_ids:  # If heavy chain exists in mock
        assert 'H' in chain_ids
    if 'L' in chain_ids:  # If light chain exists in mock
        assert 'L' in chain_ids
    
    # 2. Should not have HETATM records
    hetatm_count = 0
    for model in structure:
        for chain in model:
            for residue in chain:
                if residue.id[0] != " ":  # Non-standard residue
                    hetatm_count += 1
    assert hetatm_count == 0  # No HETATM records


def test_get_chain_info_with_invalid_settings(mock_rfantibody_dir):
    """Test getting chain info with invalid settings falls back to name-based mapping."""
    # Get the path to the mock PDB file
    pdb_path = os.path.join(mock_rfantibody_dir, "result-batch1", "design1",
                            "rf2", "samples_design_0_dldesign_0_best.pdb")

    # Create settings with invalid chain mappings (chains that don't exist in PDB)
    settings = {
        "antigenChain": "A",  # This chain doesn't exist in the mock PDB
        "heavyChain": "B",    # This chain doesn't exist in the mock PDB
        "lightChain": "C"     # This chain doesn't exist in the mock PDB
    }

    # Get chain info
    chain_map = get_chain_info(pdb_path, settings)

    # Should fall back to name-based mapping: H->H, L->L, X->C
    assert 'H' in chain_map
    assert 'L' in chain_map
    assert 'X' in chain_map
    assert chain_map['H'] == 'H'  # H chain should map to H
    assert chain_map['L'] == 'L'  # L chain should map to L
    assert chain_map['X'] == 'C'  # Other chains should map to C (antigen)


def test_process_rfantibody_pdbs(mock_rfantibody_dir, temp_dir):
    """Test processing all PDBs in an RFAntibody output directory."""
    # Define output directory
    output_dir = os.path.join(temp_dir, "cleaned_pdbs")

    # Process PDBs
    results = process_rfantibody_pdbs(mock_rfantibody_dir, output_dir)

    # Check results
    assert 'design1' in results
    assert os.path.exists(results['design1'])
    assert os.path.basename(results['design1']) == 'design1.pdb'
