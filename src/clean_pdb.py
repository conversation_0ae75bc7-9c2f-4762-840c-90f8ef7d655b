#!/usr/bin/env python
"""
clean_pdb.py: Module for cleaning and standardizing PDB files from RFAntibody output.

This module applies a series of transformations to PDB files to standardize chain IDs,
remove heteroatoms, and select the first alternate conformation where applicable.
"""

import os
import subprocess
import tempfile
import shutil
from Bio.PDB import <PERSON><PERSON><PERSON><PERSON><PERSON>, PDBIO, Select
from pathlib import Path


class ChainSelect(Select):
    """Selector for specific chains in a PDB file."""
    
    def __init__(self, chain_ids):
        """
        Initialize with chains to keep.
        
        Args:
            chain_ids (list): List of chain IDs to keep
        """
        self.chain_ids = chain_ids
        
    def accept_chain(self, chain):
        """Filter chains by ID."""
        return chain.id in self.chain_ids


def run_pdbtools(input_pdb, output_pdb, cmd_name, params=None):
    """
    Run a command from pdb-tools on the input PDB file.
    
    Args:
        input_pdb (str): Path to input PDB file
        output_pdb (str): Path to output PDB file
        cmd_name (str): pdb-tools command name (e.g., 'pdb_selalt', 'pdb_delhetatm')
        params (list, optional): Parameters for the command
        
    Returns:
        bool: True if successful, False otherwise
    """
    params = params or []
    
    # Get conda environment bin directory
    conda_prefix = os.environ.get('CONDA_PREFIX', '')
    
    # Construct full path to the pdb-tools command
    if conda_prefix:
        cmd_path = os.path.join(conda_prefix, 'bin', cmd_name)
    else:
        cmd_path = cmd_name
        
    try:
        # Run the command using subprocess
        with open(input_pdb, 'r') as f_in, open(output_pdb, 'w') as f_out:
            subprocess.run([cmd_path, *params], stdin=f_in, stdout=f_out, check=True)
        return True
    except FileNotFoundError:
        # If the command was not found, try alternative approach
        if cmd_name == "pdb_selalt":
            # Try pdb_selaltloc instead
            return run_pdbtools(input_pdb, output_pdb, "pdb_selaltloc", params)
        print(f"Error: Command '{cmd_name}' not found. Make sure pdb-tools is installed.")
        return False
        
    except subprocess.CalledProcessError as e:
        print(f"Error running {cmd_name}: {e}")
        return False


def get_chain_info(pdb_file, settings_json=None):
    """
    Get chain information from PDB file and settings.json if available.
    
    Args:
        pdb_file (str): Path to PDB file
        settings_json (dict, optional): Settings from JSON file
        
    Returns:
        dict: Dictionary mapping original chain IDs to standardized chain IDs
    """
    parser = PDBParser(QUIET=True)
    structure = parser.get_structure("temp", pdb_file)
    
    # Get all chain IDs in the PDB
    orig_chains = [chain.id for chain in structure.get_chains()]
    print(f"Original chains in {os.path.basename(pdb_file)}: {', '.join(orig_chains)}")
    
    # If settings_json is provided, use its chain mappings
    if settings_json:
        chain_map = {}
        
        # Map antigen chain - important to do this first
        if 'antigenChain' in settings_json:
            antigen_chain = settings_json['antigenChain']
            if antigen_chain in orig_chains:
                chain_map[antigen_chain] = 'C'  # Use 'C' consistently for antigen chain
                print(f"Mapping antigen chain {antigen_chain} -> C")
            else:
                print(f"Warning: Antigen chain {antigen_chain} not found in PDB")
            
        # Map heavy chain
        if 'heavyChain' in settings_json:
            heavy_chain = settings_json['heavyChain']
            if heavy_chain in orig_chains:
                chain_map[heavy_chain] = 'H'
                print(f"Mapping heavy chain {heavy_chain} -> H")
            else:
                print(f"Warning: Heavy chain {heavy_chain} not found in PDB")
            
        # Map light chain
        if 'lightChain' in settings_json:
            light_chain = settings_json['lightChain']
            if light_chain in orig_chains:
                chain_map[light_chain] = 'L'
                print(f"Mapping light chain {light_chain} -> L")
            else:
                print(f"Warning: Light chain {light_chain} not found in PDB")
            
        # For any remaining chains, assign sequential letters
        remaining_chains = [c for c in orig_chains if c not in chain_map]
        next_id = ord('C')  # Start with 'C' since 'B' is for antigen and 'A' is reserved
        for chain in remaining_chains:
            while chr(next_id) in ['H', 'L', 'B', 'A']:  # Skip already used IDs
                next_id += 1
            chain_map[chain] = chr(next_id)
            print(f"Mapping remaining chain {chain} -> {chr(next_id)}")
            next_id += 1
            
        return chain_map
    
    # If no settings provided, create a default mapping based on chain names
    print("No settings provided, using default chain mapping based on chain names")
    chain_map = {}

    # Map chains based on their names: H=Heavy, L=Light, remaining=antigen
    heavy_chain = None
    light_chain = None
    antigen_chains = []

    for chain_id in orig_chains:
        if chain_id.upper() == 'H':
            heavy_chain = chain_id
            chain_map[chain_id] = 'H'
            print(f"Mapping heavy chain {chain_id} -> H")
        elif chain_id.upper() == 'L':
            light_chain = chain_id
            chain_map[chain_id] = 'L'
            print(f"Mapping light chain {chain_id} -> L")
        else:
            antigen_chains.append(chain_id)

    # Map antigen chains (all remaining chains)
    if antigen_chains:
        # Use 'C' for the first antigen chain
        chain_map[antigen_chains[0]] = 'C'
        print(f"Mapping antigen chain {antigen_chains[0]} -> C")

        # Map additional antigen chains if any
        next_id = ord('D')  # Start with 'D' for additional antigen chains
        for chain in antigen_chains[1:]:
            while chr(next_id) in ['H', 'L', 'A', 'B']:  # Skip reserved IDs
                next_id += 1
            chain_map[chain] = chr(next_id)
            print(f"Mapping additional antigen chain {chain} -> {chr(next_id)}")
            next_id += 1
            
    return chain_map


def rename_chains(input_pdb, output_pdb, chain_map):
    """
    Rename chains in the PDB file according to the chain map.
    
    Args:
        input_pdb (str): Path to input PDB file
        output_pdb (str): Path to output PDB file
        chain_map (dict): Dictionary mapping original chain IDs to new chain IDs
        
    Returns:
        bool: True if successful, False otherwise
    """
    parser = PDBParser(QUIET=True)
    structure = parser.get_structure("temp", input_pdb)
    
    # Rename chains
    for chain in structure.get_chains():
        if chain.id in chain_map:
            chain.id = chain_map[chain.id]
    
    # Write the modified structure
    io = PDBIO()
    io.set_structure(structure)
    io.save(output_pdb)
    
    return True


def clean_pdb(input_pdb, output_pdb, settings_json=None):
    """
    Clean a PDB file by removing alternate conformations, heteroatoms, and standardizing chain IDs.

    Args:
        input_pdb (str): Path to input PDB file
        output_pdb (str): Path to output PDB file
        settings_json (dict, optional): Settings from JSON file

    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Cleaning PDB file: {os.path.basename(input_pdb)}")
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create temporary files for intermediate steps
        temp_files = [os.path.join(temp_dir, f"step{i}.pdb") for i in range(4)]
        
        # Step 1: Select first alternate conformation
        success = run_pdbtools(input_pdb, temp_files[0], "pdb_selaltloc", ["-A"])
        if not success:
            return False
            
        # Step 2: Remove heteroatoms
        success = run_pdbtools(temp_files[0], temp_files[1], "pdb_delhetatm")
        if not success:
            return False
            
        # Step 3: Renumber atoms
        success = run_pdbtools(temp_files[1], temp_files[2], "pdb_reatom")
        if not success:
            return False
            
        # Step 4: Rename chains
        chain_map = get_chain_info(temp_files[2], settings_json)
        success = rename_chains(temp_files[2], temp_files[3], chain_map)
        if not success:
            return False
            
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_pdb)
        os.makedirs(output_dir, exist_ok=True)
            
        # Copy the final result to the output path
        shutil.copy(temp_files[3], output_pdb)
        
        return True
        
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir)


def process_rfantibody_pdbs(rfantibody_dir, output_dir):
    """
    Process all PDBs in RFAntibody output directories.
    
    Args:
        rfantibody_dir (str): Path to RFAntibody job directory
        output_dir (str): Directory to write cleaned PDB files
        
    Returns:
        dict: Dictionary mapping design names to paths of cleaned PDB files
    """
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    results = {}
    
    # Find all batch directories
    batch_dirs = []
    for item in os.listdir(rfantibody_dir):
        if item.startswith('result-') and os.path.isdir(os.path.join(rfantibody_dir, item)):
            batch_dirs.append(os.path.join(rfantibody_dir, item))
    
    # Process each batch
    for batch_dir in batch_dirs:
        batch_name = os.path.basename(batch_dir).replace('result-', '')
        
        # Find all design directories
        for item in os.listdir(batch_dir):
            design_dir = os.path.join(batch_dir, item)
            if not os.path.isdir(design_dir):
                continue
                
            design_name = os.path.basename(design_dir)
            
            # Find the settings.json
            json_path = os.path.join(design_dir, 'settings.json')
            settings_json = None
            if os.path.exists(json_path):
                import json
                with open(json_path, 'r') as f:
                    settings_json = json.load(f)
            
            # Find the PDB file
            pdb_file = None
            rf2_dir = os.path.join(design_dir, 'rf2')
            if os.path.exists(rf2_dir):
                for file in os.listdir(rf2_dir):
                    if file.endswith('.pdb') and 'best' in file:
                        pdb_file = os.path.join(rf2_dir, file)
                        break
            
            if pdb_file and os.path.exists(pdb_file):
                output_pdb = os.path.join(output_dir, f"{design_name}.pdb")

                print(f"Processing design: {design_name}")
                print(f"  Input PDB: {pdb_file}")
                print(f"  Output PDB: {output_pdb}")

                # Clean the PDB file
                success = clean_pdb(pdb_file, output_pdb, settings_json)

                if success:
                    results[design_name] = output_pdb
                    print(f"  Successfully cleaned PDB for {design_name}")
                else:
                    print(f"  Failed to clean PDB for {design_name}")
    
    return results


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python clean_pdb.py <rfantibody_dir> <output_dir>")
        sys.exit(1)
        
    rfantibody_dir = sys.argv[1]
    output_dir = sys.argv[2]
    
    results = process_rfantibody_pdbs(rfantibody_dir, output_dir)
    
    # Print summary
    print(f"Cleaned PDB files for {len(results)} designs:")
    for design_name, output_path in results.items():
        print(f"  - {design_name}: {output_path}")
