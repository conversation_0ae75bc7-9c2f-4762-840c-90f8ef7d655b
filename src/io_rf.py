#!/usr/bin/env python
"""
io_rf.py: Module for parsing RFAntibody output files and extracting relevant information.

This module reads RFAntibody output files, extracts sequences, and creates FASTA files
for heavy and light chains.
"""

import os
import json
import csv
import pandas as pd
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
import re


def parse_job_json(json_path):
    """
    Parse RFAntibody job JSON file to extract metadata.
    
    Args:
        json_path (str): Path to settings.json file
        
    Returns:
        dict: Dictionary containing job metadata
    """
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    # Extract relevant metadata
    metadata = {
        'target_file': data.get('targetFile', ''),
        'antibody_file': data.get('antibodyFile', ''),
        'job_type': data.get('type', ''),
        'task': data.get('task', ''),
        'framework': data.get('framework', ''),
        'antigen_chain': data.get('antigenChain', ''),
        'heavy_chain': data.get('heavyChain', ''),
        'light_chain': data.get('lightChain', ''),
        'hotspots': data.get('hotspots', ''),
        'design_name': data.get('design', ''),
        'batch': data.get('batch', '')
    }
    
    return metadata


def parse_scores_csv(scores_path):
    """
    Parse RFAntibody scores CSV file to extract metrics.
    
    Args:
        scores_path (str): Path to *-scores.csv file
        
    Returns:
        DataFrame: Pandas DataFrame containing metrics for each design
    """
    scores_df = pd.read_csv(scores_path)
    return scores_df


def extract_sequences_from_scores(scores_df, batch_name="unknown"):
    """
    Extract heavy, light chain, and antigen sequences from scores DataFrame.
    
    Args:
        scores_df (DataFrame): Pandas DataFrame containing scores and sequences
        batch_name (str): Batch name to use as fallback for design names
        
    Returns:
        dict: Dictionary mapping design names to a dict with 'heavy', 'light', and 'antigen' sequences
    """
    sequences = {}
    
    # Print column names for debugging
    print(f"Available columns in scores file: {list(scores_df.columns)}")
    
    # Check for job name column
    job_name_col = None
    if 'Job Name' in scores_df.columns:
        job_name_col = 'Job Name'
        print(f"Using '{job_name_col}' as the job name column")
    else:
        print(f"'Job Name' column not found, will use row index with batch name '{batch_name}' as fallback")
    
    # Check for sequence columns in different formats
    seq_column = None
    heavy_col = None
    light_col = None
    antigen_col = None
    
    # Look for combined sequence column
    for col_name in ['Sequence - rfantibody', 'Sequence']:
        if col_name in scores_df.columns:
            seq_column = col_name
            print(f"Found combined sequence column: {seq_column}")
            break
    
    # Look for separate sequence columns
    for col in scores_df.columns:
        if 'heavy' in col.lower() or '_h' in col.lower():
            heavy_col = col
            print(f"Found heavy chain column: {heavy_col}")
        elif 'light' in col.lower() or '_l' in col.lower():
            light_col = col
            print(f"Found light chain column: {light_col}")
        elif 'antigen' in col.lower() or 'target' in col.lower() or '_t' in col.lower():
            antigen_col = col
            print(f"Found antigen column: {antigen_col}")
    
    for idx, row in scores_df.iterrows():
        try:
            # Get design name from job name column or create one from batch name and index
            if job_name_col and not pd.isna(row[job_name_col]):
                design_name = str(row[job_name_col]).strip()
            else:
                design_name = f"{batch_name}_design_{idx+1}"
            
            # Initialize with empty sequences
            sequences[design_name] = {'heavy': "", 'light': "", 'antigen': ""}
            
            # Try to get sequences from separate columns first
            if heavy_col and not pd.isna(row[heavy_col]):
                sequences[design_name]['heavy'] = str(row[heavy_col])
            
            if light_col and not pd.isna(row[light_col]):
                sequences[design_name]['light'] = str(row[light_col])
            
            if antigen_col and not pd.isna(row[antigen_col]):
                sequences[design_name]['antigen'] = str(row[antigen_col])
            
            # If separate columns didn't provide all sequences, try combined column
            if seq_column and not pd.isna(row[seq_column]):
                all_seqs = str(row[seq_column]).split(':')
                
                # Fill in any missing sequences
                if not sequences[design_name]['heavy'] and len(all_seqs) > 0:
                    sequences[design_name]['heavy'] = all_seqs[0]
                
                if len(all_seqs) >= 3:
                    # Format is HEAVY:LIGHT:ANTIGEN
                    if not sequences[design_name]['light']:
                        sequences[design_name]['light'] = all_seqs[1]
                    if not sequences[design_name]['antigen']:
                        sequences[design_name]['antigen'] = all_seqs[2]
                elif len(all_seqs) == 2:
                    # Determine if second part is light chain or antigen
                    second_seq = all_seqs[1]
                    if len(second_seq) > 300:  # Antigen length threshold
                        if not sequences[design_name]['antigen']:
                            sequences[design_name]['antigen'] = second_seq
                    else:
                        if not sequences[design_name]['light']:
                            sequences[design_name]['light'] = second_seq
            
            print(f"Extracted sequences for design '{design_name}'")
        except Exception as e:
            print(f"Error processing row {idx}: {e}")
            continue
    
    return sequences


def write_fasta(sequences, output_dir):
    """
    Write antibody sequences to FASTA files - individual chains and combined file.
    
    Args:
        sequences (dict): Dictionary mapping design names to sequences
        output_dir (str): Directory to write FASTA files
        
    Returns:
        dict: Dictionary mapping design names to paths of created files
    """
    os.makedirs(output_dir, exist_ok=True)
    
    output_files = {}
    
    for design_name, seqs in sequences.items():
        antibody_records = []
        file_paths = {}
        
        # Create heavy chain record
        if seqs.get('heavy'):
            hc_file = os.path.join(output_dir, f"{design_name}_H.fasta")
            hc_record = SeqRecord(
                Seq(seqs['heavy']),
                id=f"{design_name}_H",
                description="Heavy chain sequence from RFAntibody"
            )
            antibody_records.append(hc_record)
            
            # Write individual heavy chain file
            with open(hc_file, 'w') as f:
                SeqIO.write(hc_record, f, "fasta")
            file_paths['heavy'] = hc_file
            
        # Create light chain record if it exists
        if seqs.get('light') and seqs['light']:
            lc_file = os.path.join(output_dir, f"{design_name}_L.fasta")
            lc_record = SeqRecord(
                Seq(seqs['light']),
                id=f"{design_name}_L",
                description="Light chain sequence from RFAntibody"
            )
            antibody_records.append(lc_record)
            
            # Write individual light chain file
            with open(lc_file, 'w') as f:
                SeqIO.write(lc_record, f, "fasta")
            file_paths['light'] = lc_file
        
        # Create antigen record if it exists
        if seqs.get('antigen') and seqs['antigen']:
            ag_file = os.path.join(output_dir, f"{design_name}_antigen.fasta")
            ag_record = SeqRecord(
                Seq(seqs['antigen']),
                id=f"{design_name}_antigen",
                description="Antigen sequence from RFAntibody"
            )
            
            # Write individual antigen file
            with open(ag_file, 'w') as f:
                SeqIO.write(ag_record, f, "fasta")
            file_paths['antigen'] = ag_file
        
        # Create combined antibody file (heavy + light if available)
        if antibody_records:
            combined_file = os.path.join(output_dir, f"{design_name}_combined.fasta")
            with open(combined_file, 'w') as f:
                SeqIO.write(antibody_records, f, "fasta")
            file_paths['combined'] = combined_file
        
        output_files[design_name] = file_paths
    
    return output_files


def extract_antibody_sequences(rfantibody_dir, output_dir):
    """
    Extract antibody sequences from RFAntibody output directory.
    
    Args:
        rfantibody_dir (str): Path to RFAntibody job directory
        output_dir (str): Directory to write FASTA files
        
    Returns:
        dict: Dictionary containing metadata and file paths
    """
    # Find scores CSV files
    scores_files = []
    for root, _, files in os.walk(rfantibody_dir):
        for file in files:
            if file.endswith('-scores.csv'):
                scores_files.append(os.path.join(root, file))
    
    # Fallback for direct CSV files in the root directory
    if not scores_files:
        for file in os.listdir(rfantibody_dir):
            if file.endswith('-scores.csv'):
                scores_files.append(os.path.join(rfantibody_dir, file))
    
    print(f"Found {len(scores_files)} scores files: {scores_files}")
    results = {}
    
    for scores_file in scores_files:
        batch_name = os.path.basename(scores_file).replace('-scores.csv', '')
        
        # Parse scores file
        scores_df = parse_scores_csv(scores_file)
        
        # Extract sequences, passing batch name for fallback design names
        sequences = extract_sequences_from_scores(scores_df, batch_name)
        
        # Find corresponding result directory
        result_dir = os.path.join(rfantibody_dir, f"result-{batch_name}")
        if not os.path.exists(result_dir):
            continue
            
        # Determine job name column for metrics extraction
        job_name_col = 'Job Name' if 'Job Name' in scores_df.columns else scores_df.columns[0]
        
        # For each design, find its JSON and extract metadata
        for design_name in sequences.keys():
            design_dir = os.path.join(result_dir, design_name)
            if not os.path.exists(design_dir):
                continue
                
            json_path = os.path.join(design_dir, 'settings.json')
            if os.path.exists(json_path):
                metadata = parse_job_json(json_path)
            else:
                metadata = {}
            
            # Write sequences to FASTA
            fasta_files = write_fasta({design_name: sequences[design_name]}, output_dir)
            
            # Add metrics from scores DataFrame
            metrics = {}
            try:
                if not scores_df.empty:
                    # Try to find the row with matching design name
                    matching_rows = scores_df[scores_df[job_name_col] == design_name]
                    if not matching_rows.empty:
                        metrics = matching_rows.iloc[0].to_dict()
                    else:
                        # If no matching row found, try to find by index if design name was generated from batch
                        if design_name.startswith(f"{batch_name}_design_"):
                            try:
                                idx = int(design_name.split('_')[-1]) - 1
                                if 0 <= idx < len(scores_df):
                                    metrics = scores_df.iloc[idx].to_dict()
                            except (ValueError, IndexError):
                                pass
            except Exception as e:
                print(f"Error extracting metrics for {design_name}: {e}")
            
            # Store results
            results[design_name] = {
                'metadata': metadata,
                'metrics': metrics,
                'files': fasta_files.get(design_name, {})
            }
    
    return results


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python io_rf.py <rfantibody_dir> <output_dir>")
        sys.exit(1)
        
    rfantibody_dir = sys.argv[1]
    output_dir = sys.argv[2]
    
    results = extract_antibody_sequences(rfantibody_dir, output_dir)
    
    # Print summary
    print(f"Extracted sequences for {len(results)} designs:")
    for design_name, data in results.items():
        print(f"  - {design_name}:")
        if 'heavy' in data['files']:
            print(f"    Heavy chain: {data['files']['heavy']}")
        if 'light' in data['files']:
            print(f"    Light chain: {data['files']['light']}")
