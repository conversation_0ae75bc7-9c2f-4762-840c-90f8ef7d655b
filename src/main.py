#!/usr/bin/env python
"""
main.py: Main entry point for the RFAntibody extraction pipeline.

This script orchestrates the entire pipeline, from parsing RFAntibody output files 
to generating epitope maps and calculating interface metrics.
"""

import os
import sys
import argparse
import json
import pandas as pd
from pathlib import Path

# Use relative imports when running as a package
try:
    from io_rf import extract_antibody_sequences
    from clean_pdb import process_rfantibody_pdbs
    from compute_contacts import process_cleaned_pdbs, get_epitope_residues_string
    from interface_metrics import (
        process_pdb_interfaces, 
        update_metadata_with_interfaces,
        update_metadata_with_epitopes,
        update_metadata_with_all_metrics
    )
except ImportError:
    # Fallback for when running as a script
    from src.io_rf import extract_antibody_sequences
    from src.clean_pdb import process_rfantibody_pdbs
    from src.compute_contacts import process_cleaned_pdbs, get_epitope_residues_string
    from src.interface_metrics import (
        process_pdb_interfaces, 
        update_metadata_with_interfaces,
        update_metadata_with_epitopes,
        update_metadata_with_all_metrics
    )


def create_metadata_csv(results, output_path):
    """
    Create a metadata CSV file from the results of sequence extraction.
    
    Args:
        results (dict): Results from extract_antibody_sequences
        output_path (str): Path to output CSV file
    """
    metadata = []
    
    for design_name, data in results.items():
        # Extract metrics
        metrics = data.get('metrics', {})
        
        # Extract metadata
        metadata_dict = data.get('metadata', {})
        
        # Get sequences - check both combined and separate columns
        heavy_seq = ''
        light_seq = ''
        antigen_seq = ''

        # First try separate columns (exact rfantibody column names)
        if 'Sequence_H - rfantibody' in metrics:
            heavy_seq = metrics.get('Sequence_H - rfantibody', '')
        if 'Sequence_L - rfantibody' in metrics:
            light_seq = metrics.get('Sequence_L - rfantibody', '')
        if 'Sequence_T - rfantibody' in metrics:
            antigen_seq = metrics.get('Sequence_T - rfantibody', '')

        # If separate columns didn't provide sequences, try combined column
        if not heavy_seq and not light_seq and not antigen_seq:
            all_seqs = metrics.get('Sequence - rfantibody', '').split(':') if 'Sequence - rfantibody' in metrics else []
            heavy_seq = all_seqs[0] if len(all_seqs) > 0 else ''

            if len(all_seqs) >= 3:
                # If 3+ parts, format is HEAVY:LIGHT:ANTIGEN
                light_seq = all_seqs[1]
                antigen_seq = all_seqs[2]
            elif len(all_seqs) == 2:
                # Determine if second part is light chain or antigen using the same heuristic
                # as in extract_sequences_from_scores
                if len(all_seqs[1]) > 300:
                    antigen_seq = all_seqs[1]
                else:
                    light_seq = all_seqs[1]
        
        # Combine into one flat dictionary
        entry = {
            'design_name': design_name,
            'target_file': metadata_dict.get('target_file', ''),
            'antibody_file': metadata_dict.get('antibody_file', ''),
            'framework': metadata_dict.get('framework', ''),
            'heavy_chain_sequence': heavy_seq,
            'light_chain_sequence': light_seq,
            'antigen_sequence': antigen_seq,  # Added antigen sequence
            'pae': metrics.get('pae - rfantibody', ''),
            'interaction_pae': metrics.get('interaction_pae - rfantibody', ''),
            'pred_lddt': metrics.get('pred_lddt - rfantibody', ''),
            'heavy_chain_fasta': data.get('files', {}).get('heavy', ''),
            'light_chain_fasta': data.get('files', {}).get('light', ''),
            'antigen_fasta': data.get('files', {}).get('antigen', ''),  # Added antigen FASTA file path
            'combined_fasta': data.get('files', {}).get('combined', '')  # Added combined FASTA file path
        }
        
        metadata.append(entry)
    
    # Create DataFrame and save to CSV
    df = pd.DataFrame(metadata)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    df.to_csv(output_path, index=False)
    
    return df


def run_pipeline(rfantibody_dir, output_dir, distance_cutoff=5.0):
    """
    Run the complete RFAntibody extraction pipeline.
    
    Args:
        rfantibody_dir (str): Path to RFAntibody job directory
        output_dir (str): Base output directory
        distance_cutoff (float): Distance cutoff for contacts in Angstroms
        
    Returns:
        dict: Dictionary with paths to all output files
    """
    print("Starting RFAntibody extraction pipeline...")
    
    # Define output directories
    fasta_dir = os.path.join(output_dir, "fasta")
    pdb_dir = os.path.join(output_dir, "pdb_clean")
    epitope_dir = os.path.join(output_dir, "epitope")
    
    # Ensure all directories exist
    os.makedirs(fasta_dir, exist_ok=True)
    os.makedirs(pdb_dir, exist_ok=True)
    os.makedirs(epitope_dir, exist_ok=True)
    
    # Step 1: Extract sequences
    print("\nStep 1: Extracting antibody sequences...")
    seq_results = extract_antibody_sequences(rfantibody_dir, fasta_dir)
    
    # Create initial metadata CSV
    print("Creating metadata CSV...")
    metadata_path = os.path.join(output_dir, "metadata.csv")
    create_metadata_csv(seq_results, metadata_path)
    
    # Step 2: Clean PDB files
    print("\nStep 2: Cleaning PDB files...")
    pdb_results = process_rfantibody_pdbs(rfantibody_dir, pdb_dir)
    
    # Step 3: Find epitope residues
    print("\nStep 3: Finding epitope residues...")
    try:
        # Based on our chain mapping observations, use chain 'C' as the antigen chain
        # since clean_pdb.py maps the antigen to 'C'
        antigen_chain = 'C'
        print(f"Using chain '{antigen_chain}' as antigen chain for epitope mapping")
        
        # Process with the appropriate antigen chain
        epitope_results = process_cleaned_pdbs(pdb_dir, epitope_dir, distance_cutoff, antigen_chain)
        print(f"Successfully processed {len(epitope_results)} epitope maps")
    except Exception as e:
        print(f"Error in epitope mapping: {e}")
        print("Continuing with pipeline...")
    
    # Step 4: Calculate interface metrics
    print("\nStep 4: Calculating interface metrics...")
    interface_csv = os.path.join(output_dir, "interface_metrics.csv")
    try:
        # Modified to use chain 'C' as antigen
        antigen_chain = 'C'
        print(f"Using chain '{antigen_chain}' as antigen chain for interface calculations")
        process_pdb_interfaces(pdb_dir, interface_csv, antigen_chain=antigen_chain)
    except ImportError:
        print("Warning: FreeSASA not available, skipping interface metrics calculation")
    except Exception as e:
        print(f"Error calculating interface metrics: {e}")
        print("Creating empty interface metrics file...")
        # Create an empty interface metrics file
        with open(interface_csv, 'w') as f:
            f.write("design_name,total_interface_area,heavy_chain_interface,light_chain_interface\n")
    
    # Always try to update metadata, even if previous steps had errors
    try:
        print("Updating metadata with interface metrics and epitope data...")
        final_metadata_path = os.path.join(output_dir, "metadata_complete.csv")
        update_metadata_with_all_metrics(metadata_path, interface_csv, epitope_dir, final_metadata_path)
        print(f"Metadata with epitope data saved to {final_metadata_path}")
    except Exception as e:
        print(f"Error updating metadata with epitope data: {e}")
        # Fall back to just interface metrics if epitope update fails
        try:
            print("Falling back to just interface metrics...")
            update_metadata_with_interfaces(metadata_path, interface_csv, final_metadata_path)
            print(f"Metadata with interface metrics saved to {final_metadata_path}")
        except Exception as e2:
            print(f"Error updating metadata with interface metrics: {e2}")
            print(f"Original metadata still available at {metadata_path}")
    
    print("\nPipeline complete!")
    return {
        "metadata_csv": metadata_path,
        "pdb_dir": pdb_dir,
        "fasta_dir": fasta_dir,
        "epitope_dir": epitope_dir
    }


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="RFAntibody extraction pipeline")
    parser.add_argument("rfantibody_dir", help="Path to RFAntibody job directory")
    parser.add_argument("--output", "-o", default="./data/processed", help="Base output directory")
    parser.add_argument("--distance", "-d", type=float, default=5.0, help="Distance cutoff for contacts (Angstroms)")
    
    args = parser.parse_args()
    
    run_pipeline(args.rfantibody_dir, args.output, args.distance)
